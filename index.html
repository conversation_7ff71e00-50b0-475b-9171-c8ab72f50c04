<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>RnD Analytics POC - GTM & GA4</title>

  <!-- ✅ Google Tag Manager -->
  <script>
    (function(w,d,s,l,i){w[l]=w[l]||[];
    w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
    var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;
    j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
    f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-TJ39Q86V');
  </script>

  <!-- ✅ Google Analytics (GA4) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-ZJJES2DN0B"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){ dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-ZJJES2DN0B');
  </script>

  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
    }
    .container {
      max-width: 1000px;
      margin: auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 40px;
    }
    .header h1 {
      font-size: 2.5rem;
    }
    .card, .test-section {
      background: #fff;
      color: #333;
      padding: 20px;
      margin-bottom: 30px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    .card h3 {
      margin-top: 0;
      color: #4a5568;
    }
    .btn {
      display: inline-block;
      margin: 10px 10px 0 0;
      padding: 10px 20px;
      border: none;
      color: #fff;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
    }
    .btn-primary { background: #667eea; }
    .btn-secondary { background: #f093fb; }
    .btn-success { background: #00f2fe; }
    .event-log {
      background: #1a202c;
      color: #e2e8f0;
      padding: 10px;
      font-family: monospace;
      font-size: 0.9rem;
      max-height: 200px;
      overflow-y: auto;
      border-radius: 6px;
      margin-top: 10px;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      font-size: 0.9rem;
      color: rgba(255,255,255,0.6);
    }
  </style>
</head>
<body>

  <!-- ✅ GTM Noscript Fallback -->
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TJ39Q86V"
    height="0" width="0" style="display:none;visibility:hidden"></iframe>
  </noscript>

  <div class="container">
    <div class="header">
      <h1>RnD Analytics POC</h1>
      <p>Dual Tracking using Google Tag Manager & GA4</p>
    </div>

    <div class="card">
      <h3>🛠 Tracking Details</h3>
      <p><strong>GTM ID:</strong> GTM-TJ39Q86V</p>
      <p><strong>GA4 ID:</strong> G-ZJJES2DN0B</p>
      <p><strong>Status:</strong> Both Active ✅</p>
    </div>

    <div class="test-section">
      <h3>🧪 Test Analytics Events</h3>
      <button class="btn btn-primary" onclick="trackPageView()">Track Page View</button>
      <button class="btn btn-secondary" onclick="trackCustomEvent()">Custom Event</button>
      <button class="btn btn-success" onclick="trackConversion()">Track Conversion</button>
      <button class="btn btn-primary" onclick="trackUserEngagement()">User Engagement</button>
      <button class="btn btn-secondary" onclick="debugTracking()">Debug Tracking</button>
      <div id="eventLog" class="event-log">
        <div>✅ Analytics initialized...</div>
      </div>
    </div>

    <div class="footer">
      <p>© 2025 RnD Analytics POC. Built with ❤️ using GTM & GA4</p>
    </div>
  </div>

  <script>
    function logEvent(message) {
      const log = document.getElementById('eventLog');
      const time = new Date().toLocaleTimeString();
      const entry = document.createElement('div');
      entry.textContent = `[${time}] ${message}`;
      log.appendChild(entry);
      log.scrollTop = log.scrollHeight;
    }

    function trackPageView() {
      gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href
      });
      dataLayer.push({
        event: 'gtm_page_view',
        page_title: document.title,
        page_url: window.location.href
      });
      logEvent("📄 Page view tracked.");
    }

    function trackCustomEvent() {
      gtag('event', 'custom_button_click', {
        event_category: 'test',
        event_label: 'Custom Button'
      });
      dataLayer.push({
        event: 'gtm_custom_click',
        event_category: 'test',
        event_label: 'Custom Button'
      });
      logEvent("🎯 Custom event tracked.");
    }

    function trackConversion() {
      gtag('event', 'purchase', {
        transaction_id: 'TXN_' + Date.now(),
        value: 200,
        currency: 'USD'
      });
      dataLayer.push({
        event: 'gtm_purchase',
        transaction_id: 'TXN_' + Date.now(),
        value: 200,
        currency: 'USD'
      });
      logEvent("💰 Conversion tracked.");
    }

    function trackUserEngagement() {
      gtag('event', 'user_engagement', {
        engagement_time_msec: 45000
      });
      dataLayer.push({
        event: 'gtm_user_engagement',
        engagement_time: '45s'
      });
      logEvent("⏱️ User engagement tracked (45s).");
    }

    function debugTracking() {
      const gaLoaded = typeof gtag !== 'undefined';
      const gtmLoaded = typeof dataLayer !== 'undefined';
      logEvent("🛠 Debug Info:");
      logEvent("📈 GA4: " + (gaLoaded ? 'Loaded ✅' : 'Not Loaded ❌'));
      logEvent("📊 GTM: " + (gtmLoaded ? 'Loaded ✅' : 'Not Loaded ❌'));
    }

    // Automatically track first page view
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(trackPageView, 800);
    });

    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', () => {
      const scrolled = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
      if (scrolled > maxScroll && scrolled % 25 === 0) {
        maxScroll = scrolled;
        gtag('event', 'scroll', { value: scrolled });
        dataLayer.push({ event: 'gtm_scroll_depth', value: scrolled });
        logEvent(`📜 Scroll depth tracked: ${scrolled}%`);
      }
    });
  </script>
</body>
</html>
